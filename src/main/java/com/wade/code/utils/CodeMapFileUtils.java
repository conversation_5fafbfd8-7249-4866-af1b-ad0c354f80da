package com.wade.code.utils;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.StringUtils;

import com.wade.code.dao.FavoriteLeaf;

public class CodeMapFileUtils {

    public static Integer adjust(FavoriteLeaf leaf) {
        if (leaf == null) {
            return null;
        }

        if (StringUtils.isBlank(leaf.getUrl())) {
            return null;
        }
        String code = leaf.getCode();
        if (StringUtils.isBlank(code)) {
            return null;
        }
        String filePath = removePrefix(leaf.getUrl());
        int targetLineNumber = leaf.getLine();

        List<String> lines = new ArrayList<>();
        try (BufferedReader reader = new BufferedReader(new FileReader(filePath))) {
            String line;
            while ((line = reader.readLine()) != null) {
                lines.add(line);
            }
        } catch (IOException e) {
            System.err.println("读取文件时出错: " + e.getMessage());
            return null;
        }

        if (targetLineNumber < 1) {
            System.err.println("指定的行号超出文件范围。");
            return null;
        }

        if( targetLineNumber >= lines.size()){
            targetLineNumber = lines.size() - 1 ;
        }

        String targetLine = lines.get(targetLineNumber);
        Integer adjustLineNumber = null;
        if (targetLine.contains(code)) {
//            System.out.println(leaf.getName() + " 无需调整行数");
            return null;
        }
        int step = 1;
        boolean found = false;
        while (!found) {
            int prevLineIndex = targetLineNumber - step - 1;
            int nextLineIndex = targetLineNumber + step - 1;

            if (prevLineIndex >= 0 && lines.get(prevLineIndex).contains(code)) {
                lines.set(targetLineNumber - 1, lines.get(prevLineIndex));
                found = true;
                adjustLineNumber = prevLineIndex;
            }
            if (nextLineIndex < lines.size() && lines.get(nextLineIndex).contains(code)) {
                lines.set(targetLineNumber - 1, lines.get(nextLineIndex));
                found = true;
                adjustLineNumber = nextLineIndex;
            }
            if (found) {
                System.out.println("调整后的行号: " + adjustLineNumber);
                break;
            }
            if (prevLineIndex < 0 && nextLineIndex >= lines.size()) {
                break;
            }
            step++;
        }
        return adjustLineNumber ;
    }

    private static String removePrefix(String url) {
        return url.replace("file://", "");
    }

    public static void main(String[] args) {
        // 示例使用
        FavoriteLeaf leaf = new FavoriteLeaf();
        adjust(leaf);
    }
}  