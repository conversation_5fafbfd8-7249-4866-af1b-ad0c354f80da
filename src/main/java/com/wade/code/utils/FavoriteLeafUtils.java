package com.wade.code.utils;

import static org.apache.commons.collections.CollectionUtils.isEmpty;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.wade.code.convert.FavoriteLeafConverter;
import com.wade.code.convert.TagConverter;
import com.wade.code.dao.FavoriteLeaf;
import com.wade.code.dao.Tag;
import com.wade.code.mapper.TagMapper;
import com.wade.code.model.FavoriteLeafVO;
import com.wade.code.model.GraphNodeLinkInfo;
import com.wade.code.model.TagVO;
import com.wade.code.service.FavoriteService;
import com.wade.code.service.LeafGraphService;

/**
 * FavoriteLeaf 相关的工具类
 */

@Component
public class FavoriteLeafUtils {
    private static TagMapper tagMapper;
    private static CodeMapCache codeMapCache;
    private static LeafGraphService leafGraphService;
    private static TagConverter tagConverter;

    @Autowired
    public void setTagMapper(TagMapper tagMapper) {
        FavoriteLeafUtils.tagMapper = tagMapper;
    }

    @Autowired
    public void setTagConverter(TagConverter tagConverter) {
        FavoriteLeafUtils.tagConverter = tagConverter;
    }

    @Autowired
    public void setCodeMapCache(CodeMapCache codeMapCache) {
        FavoriteLeafUtils.codeMapCache = codeMapCache;
    }

    @Autowired
    public void setLeafGraphService(LeafGraphService leafGraphService) {
        FavoriteLeafUtils.leafGraphService = leafGraphService;
    }

    /**
     * 将 FavoriteLeaf 转换为 FavoriteLeafVO，并设置标签
     *
     * @param leaf FavoriteLeaf 实体
     * @return FavoriteLeafVO 视图对象
     */
    public static FavoriteLeafVO convertToVO(FavoriteLeaf leaf) {
        if (leaf == null) {
            return null;
        }

        // 使用 MapStruct 进行基本属性转换
        FavoriteLeafVO leafVO = FavoriteLeafConverter.INSTANCE.toVO(leaf);

        setTags(leaf, leafVO);
        setHasGraph(leaf, leafVO);

        return leafVO;
    }

    private static void setHasGraph(FavoriteLeaf leaf, FavoriteLeafVO leafVO) {
        GraphNodeLinkInfo graphNodeLinkInfo = leafGraphService.getById(leaf.getId());
        if(graphNodeLinkInfo!=null){
            leafVO.setHasGraph(true);
        }
    }


    private static void setTags(FavoriteLeaf leaf, FavoriteLeafVO leafVO) {
        String tagBitmaps = leaf.getTagBitmaps();
        try {
            List<Integer> tagIndexes =  TagUtils.getTagIndexes(tagBitmaps);

            List<TagVO> hasTags = new ArrayList<>();

            for (Integer tagIndex : tagIndexes) {
                Map<String, Object> params = new HashMap<>();
                params.put("tag_index", tagIndex);
                List<Tag> tags = tagMapper.selectByMap(params);
                hasTags.addAll(TagConverter2.toVOs(tags, tagMapper));
            }
            leafVO.setTags(hasTags);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 将 FavoriteLeaf 列表转换为 FavoriteLeafVO 列表，并设置标签
     *
     * @param leafList FavoriteLeaf 实体列表
     * @return FavoriteLeafVO 视图对象列表
     */
    public static List<FavoriteLeafVO> convertToVOList(List<FavoriteLeaf> leafList) {
        if (leafList == null || leafList.isEmpty()) {
            return Collections.emptyList();
        }

        List<FavoriteLeafVO> result = new ArrayList<>(leafList.size());
        for (FavoriteLeaf leaf : leafList) {
            result.add(convertToVO(leaf));
        }

        return result;
    }

    /**
     * 将 FavoriteLeafVO 转换为 FavoriteLeaf，并处理标签
     *
     * @param leafVO FavoriteLeafVO 视图对象
     * @param favoriteService FavoriteService 服务
     * @return FavoriteLeaf 实体
     */
    public static FavoriteLeaf convertToEntity(FavoriteLeafVO leafVO, FavoriteService favoriteService) {
        if (leafVO == null) {
            return null;
        }

        // 使用 MapStruct 进行基本属性转换
        FavoriteLeaf leaf = FavoriteLeafConverter.INSTANCE.toEntity(leafVO);

        setTag(leaf, leafVO);

        return leaf;
    }

    private static void setTag(FavoriteLeaf leaf, FavoriteLeafVO leafVO) {

        List<TagVO> tags = leafVO.getTags();
        if (tags == null || tags.isEmpty()) {
            leaf.setTagBitmaps("");
            return;
        }

        //本地缓存

        List<Tag> sortedTags = tags.stream().map(tag -> codeMapCache.getTag(tag.getId()))
                .filter(tag -> tag != null && tag.getId()!=null && tag.getId()!=0)
                .sorted(Comparator.comparing(Tag::getTagIndex).reversed()).collect(
                        Collectors.toList());
        if(isEmpty(sortedTags)){
            return ;
        }
//        Tag tag = sortedTags.get(0);
//        int maTagIndex = tag.getTagIndex();
//
//        // 将分段的 tagIndex 转换为二进制字符串 bitTag
//        StringBuilder bitTagBuilder = new StringBuilder();
//        Set<Integer> hitTagIndexes = sortedTags.stream().map(Tag::getTagIndex).collect(Collectors.toSet());
//
//        for (int i = maTagIndex; i >= 0; i--) {
//            // 如果当前位在标签索引列表中，设置为1，否则设置为0
//            bitTagBuilder.append(hitTagIndexes.contains(i) ? '1' : '0');
//        }
//        String bitTag = bitTagBuilder.toString();
        String bitTag = TagUtils.databaseTagNumberStr(sortedTags.stream().map(Tag::getTagIndex).collect(Collectors.toList()));

        System.out.println("bitTag:" + bitTag);
        leaf.setTagBitmaps(bitTag);
    }

}
