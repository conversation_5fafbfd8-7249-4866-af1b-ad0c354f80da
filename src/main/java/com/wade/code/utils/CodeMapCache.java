package com.wade.code.utils;

import java.util.concurrent.TimeUnit;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.wade.code.dao.FavoriteBranch;
import com.wade.code.dao.Tag;
import com.wade.code.mapper.FavoriteBranchMapper;
import com.wade.code.mapper.TagMapper;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2025-03-29
 */
@Component
public class CodeMapCache {

    @Autowired
    private FavoriteBranchMapper favoriteBranchMapper;
    @Autowired
    private TagMapper tagMapper;

    private LoadingCache<Integer, FavoriteBranch> branchCache = CacheBuilder.newBuilder()
            // 设置cache的初始大小为10
            .initialCapacity(10)
            // 设置并发数为5
            .concurrencyLevel(100)
            // 设置cache中的数据在写入或者读取之后的存活时间为5秒
            .refreshAfterWrite(1, TimeUnit.MINUTES)
            // 设置最大存储的数量
            .maximumSize(5000)
            //构建cache实例
            .build(new CacheLoader<Integer, FavoriteBranch>() {
                @Override
                public FavoriteBranch load(Integer id) throws Exception {
                    FavoriteBranch favoriteBranch = favoriteBranchMapper.selectById(id);
                    if (favoriteBranch == null) {
                        return new FavoriteBranch();
                    }
                    return favoriteBranch;
                }
            });


    private LoadingCache<Integer, Tag> tagCache = CacheBuilder.newBuilder()
            // 设置cache的初始大小为10
            .initialCapacity(10)
            // 设置并发数为5
            .concurrencyLevel(100)
            // 设置cache中的数据在写入或者读取之后的存活时间为5秒
            .refreshAfterWrite(1, TimeUnit.MINUTES)
            // 设置最大存储的数量
            .maximumSize(5000)
            //构建cache实例
            .build(new CacheLoader<Integer, Tag>() {
                @Override
                public Tag load(Integer id) throws Exception {
                    Tag tag = tagMapper.selectById(id);
                    if (tag == null) {
                        return new Tag();
                    }
                    return tag;
                }
            });


    public FavoriteBranch getBranch(Integer id) {
        try {
            return branchCache.get(id);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }


    public Tag getTag(Integer id) {
        try {
            if (id == null) {
                return null ;
            }
            return tagCache.get(id);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
