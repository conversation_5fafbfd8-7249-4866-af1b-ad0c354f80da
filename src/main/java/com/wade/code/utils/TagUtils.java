package com.wade.code.utils;

import static com.wade.code.constant.Constant.TAG_SEGMENT_STRING_LENGTH;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import com.wade.code.constant.Constant;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2025-05-19
 */
public class TagUtils {


    public static Map<Integer, List<Integer>> getSegmentedTagIndexes(List<Integer> tagIndexes) {
        Map<Integer, List<Integer>> segmentedTagIndexes = new TreeMap<>();
        for (Integer tagIndex : tagIndexes) {
            if (tagIndex == null) {
                continue;
            }
            // 计算段号：每Constant.TAG_SEGMENT_BIT_SIZE位为一段
            int segmentIndex = tagIndex / Constant.TAG_SEGMENT_BIT_SIZE;

            // 将标签索引添加到对应的段中
            List<Integer> indexesInSegment =
                    segmentedTagIndexes.computeIfAbsent(segmentIndex, k -> new ArrayList<>());
            indexesInSegment.add(tagIndex % Constant.TAG_SEGMENT_BIT_SIZE); // 存储在段内的位置
        }
        return segmentedTagIndexes;
    }


    public static String databaseTagNumberStr(List<Integer> tagIndexes) {

        Map<Integer, List<Integer>> segmentedTagIndexes = getSegmentedTagIndexes(tagIndexes);
        if (MapUtils.isEmpty(segmentedTagIndexes)) {
            return "";
        }

        // 使用 Stream API 找到 keySet 中的最大值
        int maxKey = segmentedTagIndexes.keySet().stream()
                .max(Integer::compare).orElse(0);

        List<String> tagNumberList = new ArrayList<>();
        for (int i = 0; i <= maxKey; i++) {
            String tagNumberStr = StringUtils.leftPad("", TAG_SEGMENT_STRING_LENGTH, "0");

            if (segmentedTagIndexes.containsKey(i)) {
                List<Integer> indexes = segmentedTagIndexes.get(i);
                Long tagNumber = 0L;
                for (Integer j : indexes) {
                    tagNumber = (1L << j) + tagNumber;
                }
                tagNumberStr = StringUtils.leftPad(tagNumber + "", TAG_SEGMENT_STRING_LENGTH, "0");
            }
            tagNumberList.add(tagNumberStr);
        }

        return StringUtils.join(tagNumberList, "#");
    }

    public static List<Integer> getTagIndexes(String tagBitmaps) {

        if(StringUtils.isBlank(tagBitmaps)){
            return new ArrayList<>();
        }

        String[] splits = tagBitmaps.split("#");
        if (splits.length == 0) {
            return new ArrayList<>();
        }

        List<Integer> allIndexes = new ArrayList<>();
        for (int i = 0; i < splits.length; i++) {
            List<Integer> segmentIndexes = null;
            try {
                segmentIndexes = getBitPositions(Long.parseLong(splits[i]));
            } catch (NumberFormatException e) {
                continue;
            }
            for (Integer index : segmentIndexes) {
              index = index + i * Constant.TAG_SEGMENT_BIT_SIZE;
              allIndexes.add(index);
          }
        }
        return allIndexes;
    }


    public static List<Integer> getBitPositions(long value) {
        List<Integer> positions = new ArrayList<>();
        for (int i = 0; i < Constant.TAG_SEGMENT_BIT_SIZE; i++) {  // Long 是 64 位
            if ((value & (1L << i)) != 0) {  // 检查第 i 位是否为 1
                positions.add(i);
            }
        }
        return positions;
    }

}
