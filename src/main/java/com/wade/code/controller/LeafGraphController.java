package com.wade.code.controller;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.wade.code.convert.LeafGraphConverter;
import com.wade.code.dao.LeafGraph;
import com.wade.code.model.Graph;
import com.wade.code.model.GraphEdge;
import com.wade.code.model.GraphNode;
import com.wade.code.model.GraphNodeLinkInfo;
import com.wade.code.model.LeafGraphVO;
import com.wade.code.model.UpdateGraphRequest;
import com.wade.code.service.LeafGraphService;
import com.wade.code.service.LeafGraphServiceImpl;

/**
 * LeafGraph 控制器
 */
@RestController
@RequestMapping("/api/leaf/graph")
@CrossOrigin(origins = "*", methods = {RequestMethod.GET, RequestMethod.POST, RequestMethod.OPTIONS})
public class LeafGraphController {

    @Autowired
    private LeafGraphService leafGraphService;
    @Autowired
    private LeafGraphServiceImpl leafGraphServiceImpl;

    /**
     * 获取所有叶子图谱
     */
    @GetMapping("/list")
    public List<LeafGraphVO> listAll() {
        List<LeafGraph> leafGraphs = leafGraphService.listAll();
        return LeafGraphConverter.INSTANCE.toVOList(leafGraphs);
    }

    @GetMapping("/getByLeafId")
    public Graph getByLeafId(@RequestParam("leafId") Integer id) {
        GraphNodeLinkInfo nodeLinkGraph = leafGraphService.getById(id);
        Graph graph = new Graph();
        if(nodeLinkGraph == null){
            return graph ;
        }
        graph.setCurNodeLinkInfo(nodeLinkGraph);

        getGraph(nodeLinkGraph, graph,new HashSet<>());
        return graph;
    }

    private void getGraph(GraphNodeLinkInfo graphNodeLinkInfo, Graph graph,Set<GraphNode> travelNodes) {

        List<GraphNode> preNodes = graphNodeLinkInfo.getPreNodes();
        List<GraphEdge> preEdges = graphNodeLinkInfo.getPreEdges();

        List<GraphNode> nextNodes = graphNodeLinkInfo.getNextNodes();
        List<GraphEdge> nextEdges = graphNodeLinkInfo.getNextEdges();

        graph.getEdges().addAll(nextEdges);
        graph.getEdges().addAll(preEdges);

        graph.getNodes().add(graphNodeLinkInfo.getCurNode());
        graph.getNodes().addAll(nextNodes);
        graph.getNodes().addAll(preNodes);
        travelNodes.add(graphNodeLinkInfo.getCurNode());


        for (GraphNode preNode : preNodes) {
            if (preNode == null || preNode.getId() == null) {
                continue;
            }
            if(travelNodes.stream().anyMatch(node -> node!=null && node.getId().equals(preNode.getId()))){
                continue;
            }

            GraphNodeLinkInfo linkInfo = leafGraphService.getById(Integer.parseInt(preNode.getId()));
            getGraph(linkInfo, graph,travelNodes);
        }

        for (GraphNode nextNode : nextNodes) {
            if(nextNode == null || nextNode.getId() == null  ){
                continue;
            }
            if( travelNodes.stream().anyMatch(node -> node.getId().equals(nextNode.getId()))){
                continue;
            }
            GraphNodeLinkInfo linkInfo = leafGraphService.getById(Integer.parseInt(nextNode.getId()));
            getGraph(linkInfo, graph,travelNodes);
        }

    }

    /**
     * 根据名称查询叶子图谱
     */
    @GetMapping("/byName")
    public List<LeafGraphVO> getByName(@RequestParam("curName") String curName) {
        List<LeafGraph> leafGraphs = leafGraphService.getByName(curName);
        return LeafGraphConverter.INSTANCE.toVOList(leafGraphs);
    }

    /**
     * 添加叶子图谱
     */
    @PostMapping("/updateLeafGraph")
    public Object updateLeafGraph(@RequestBody UpdateGraphRequest updateGraphRequest) {
        try {
            // addNodes
            Graph newGraph = updateGraphRequest.getNewGraph();
            Graph oldGraph = updateGraphRequest.getOldGraph();

            Set<GraphNode> newNodes = newGraph.getNodes();
            Set<GraphNode> oldNodes = oldGraph.getNodes();

            // 创建一个新的列表，只包含 newNodes 中的元素
            List<GraphNode> addNodes = new ArrayList<>(newNodes);
            addNodes.removeAll(oldNodes);

            // rmNodes
            List<GraphNode> rmNodes = new ArrayList<>(oldNodes);
            rmNodes.removeAll(newNodes);


            for (GraphNode addNode : addNodes) {
                // 将 VO 转换为实体
                LeafGraph leafGraph = LeafGraphConverter.INSTANCE.toEntity(addNode,newGraph.getNodes(),newGraph.getEdges());
                if (leafGraph == null) {
                    continue;
                }
                boolean success = leafGraphService.save(leafGraph);
                System.out.println(
                        "add success" + success + ":" + leafGraph.getId() + ",name:" + leafGraph.getCurInfo());

            }

            for (GraphNode rmNode : rmNodes) {
                // 将 VO 转换为实体
                LeafGraph leafGraph = LeafGraphConverter.INSTANCE.toEntity(rmNode, newGraph.getNodes(), newGraph.getEdges());

                boolean success = leafGraphService.delete(leafGraph.getId());
                System.out.println(
                        "delete success" + success + ":" + leafGraph.getId() + ",name:" + leafGraph.getCurInfo());
            }

            return "success";

        } catch (Exception e) {
            e.printStackTrace();
            return "添加叶子图谱出错: " + e.getMessage();
        }
    }

    /**
     * 更新叶子图谱
     */
    @PutMapping("/update")
    public Object update(@RequestBody LeafGraphVO leafGraphVO) {
        try {
            System.out.println("更新叶子图谱: " + leafGraphVO);

            // 将 VO 转换为实体
            LeafGraph leafGraph = LeafGraphConverter.INSTANCE.toEntity(leafGraphVO);

            boolean success = leafGraphService.update(leafGraph);
            if (success) {
                return LeafGraphConverter.INSTANCE.toVO(leafGraph);
            } else {
                return "更新叶子图谱失败";
            }
        } catch (Exception e) {
            e.printStackTrace();
            return "更新叶子图谱出错: " + e.getMessage();
        }
    }

    /**
     * 删除叶子图谱
     */
    @DeleteMapping("/delete/{id}")
    public Object deleteLeafGraph(@PathVariable Integer id) {
        try {
            boolean success = leafGraphService.delete(id);
            if (success) {
                return "删除叶子图谱成功";
            } else {
                return "删除叶子图谱失败";
            }
        } catch (Exception e) {
            e.printStackTrace();
            return "删除叶子图谱出错: " + e.getMessage();
        }
    }
}
