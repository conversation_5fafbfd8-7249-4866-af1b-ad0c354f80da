package com.wade.code.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import com.wade.code.dao.MiddlewareInfo;
import com.wade.code.service.MiddlewareInfoService;

@RestController
@RequestMapping("/middleware")
@CrossOrigin
public class MiddlewareInfoController {

    @Autowired
    private MiddlewareInfoService middlewareInfoService;

    // 新增
    @PostMapping("/add")
    public Object addMiddleware(@RequestBody MiddlewareInfo middlewareInfo) {
        middlewareInfoService.save(middlewareInfo);

        return middlewareInfoService.list();
    }

    // 删除
    @DeleteMapping("/delete/{id}")
    public boolean deleteMiddleware(@PathVariable Integer id) {
        return middlewareInfoService.removeById(id);
    }

    // 修改
    @PutMapping("/update")
    public boolean updateMiddleware(@RequestBody MiddlewareInfo middlewareInfo) {
        return middlewareInfoService.updateById(middlewareInfo);
    }

    // 查询所有
    @GetMapping("/list")
    public List<MiddlewareInfo> listMiddleware() {
        return middlewareInfoService.list();
    }

    // 根据 ID 查询
    @GetMapping("/get/{id}")
    public MiddlewareInfo getMiddlewareById(@PathVariable Integer id) {
        return middlewareInfoService.getById(id);
    }
}