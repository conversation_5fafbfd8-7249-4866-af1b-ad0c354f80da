package com.wade.code.controller;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

import com.alibaba.fastjson.JSON;
import com.wade.code.service.LeafGraphService;
import com.wade.code.utils.FavoriteLeafUtils;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.wade.code.config.CodePathHelper;
import com.wade.code.dao.FavoriteBranch;
import com.wade.code.dao.FavoriteLeaf;
import com.wade.code.model.FavoriteBranchVO;
import com.wade.code.model.FavoriteLeafVO;
import com.wade.code.model.SearchResult;
import com.wade.code.model.TreeNode;
import com.wade.code.service.FavoriteService;

@RestController
@RequestMapping("/api/favorite")
@CrossOrigin(origins = "*", methods = {RequestMethod.GET, RequestMethod.POST, RequestMethod.OPTIONS})
public class FavoriteController {
    @Autowired
    private FavoriteService favoriteService;
    @Autowired
    private LeafGraphService leafGraphService;

    @GetMapping("/allLeafs")
    public TreeNode getAllLeafs() {
        return favoriteService.getAllLeafs();
    }


    @GetMapping("/leaf/get")
    public FavoriteLeafVO getLeaf(@RequestParam("leafId") Integer leafId) {
        FavoriteLeaf leaf =  favoriteService.getLeaf(leafId);
        return FavoriteLeafUtils.convertToVO(leaf);

    }


    @GetMapping("/leafsByBranchId")
    public List<FavoriteLeafVO> getLeafsByBranchId(@RequestParam("branchId") Integer branchId) {
        List<FavoriteLeaf> leafsByBranchId = favoriteService.getLeafsByBranchId(branchId);


        return FavoriteLeafUtils.convertToVOList(leafsByBranchId);
    }


    @PostMapping("/branchRename")
    public void branchRename(@RequestParam("id") Integer id, @RequestParam("name") String name) {
        favoriteService.renameBranch(id, name);
    }


    @GetMapping("/branch/delete")
    public Object branchDelete(@RequestParam("branchId") Integer branchId) {
        try {
            favoriteService.deleteBranch(branchId);
            return "success";
        } catch (Exception e) {
            e.printStackTrace();
            return e.getMessage();
        }
    }

    @PostMapping("/branch/add")
    public Object branchDelete(@RequestBody FavoriteBranch branch) {
        try {
            favoriteService.saveBranch(branch);
            return "success";
        } catch (Exception e) {
            e.printStackTrace();
            return e.getMessage();
        }
    }

    @PostMapping("/branch/update")
    public Object branchUpdate(@RequestBody FavoriteBranch branch) {
        try {
            favoriteService.saveBranch(branch);
            return "success";
        } catch (Exception e) {
            e.printStackTrace();
            return e.getMessage();
        }
    }


    @PostMapping("/move")
    public void moveNode(@RequestParam("dragId") Integer dragId, @RequestParam("dropId") Integer dropId) {
        favoriteService.moveNode(dragId, dropId);
    }


    @PostMapping("/moveLeafPath")
    public void moveLeafPath(@RequestBody JSONObject request) {
        FavoriteLeaf leaf = request.getObject("leaf", FavoriteLeaf.class);
        Integer branchId = request.getInteger("branchId");
        favoriteService.moveLeafPath(leaf, branchId);
    }


    public static void sortSearchResults(List<SearchResult> searchResults) {
        if (CollectionUtils.isEmpty(searchResults)) {
            return;
        }
        // 自定义比较器
        Comparator<SearchResult> comparator = (result1, result2) -> {
            FavoriteBranch branch1 = result1.getBranch();
            FavoriteLeaf leaf1 = result1.getLeaf();

            FavoriteBranch branch2 = result2.getBranch();
            FavoriteLeaf leaf2 = result2.getLeaf();

            int priority1 = 0;
            int priority2 = 0;
            if (leaf1 != null && leaf1.getPriority() != null) {
                priority1 = leaf1.getPriority();
            }
            //            else if (branch1 != null && branch1.getPriority() != null) {
            //                priority1 = branch1.getPriority();
            //            }

            if (leaf2 != null && leaf2.getPriority() != null) {
                priority2 = leaf2.getPriority();
            }
            //            else if (branch2 != null && branch2.getPriority() != null) {
            //                priority2 = branch2.getPriority();
            //            }
            // 先比较 FavoriteLeaf 的 priority
            return Integer.compare(priority2, priority1);
        };

        // 使用 Collections.sort 方法进行排序
        Collections.sort(searchResults, comparator);
    }

    @GetMapping("/searchCode")
    public List<SearchResult> searchCode(@RequestParam(value = "keyword", required = false) String keyword ,
           @RequestParam(value = "tagIds", required = false) String tagIds) {
        List<SearchResult> searchResults = favoriteService.searchCode(keyword);
        sortSearchResults(searchResults);
        return searchResults;
    }

    @GetMapping("/updateLeafPriority")
    public Object updateLeafPriority(@RequestParam(value = "leafId", required = true) Integer leafId) {
        favoriteService.updateLeafPriority(leafId);
        System.out.println("updateLeafPriority:leafId=" + leafId);
        return "success";
    }
    @GetMapping("/openIdeaFile")
    public void openIdeaFile(@RequestParam(value = "path", required = false) String path
            , @RequestParam(value = "line", required = false) String line,
            @RequestParam(value = "leafId", required = false) Integer leafId)
            throws InterruptedException, IOException {

        // 根据path和line构建命令行，然后执行cmd命令
        // 命令如 idea --line 349 /Users/<USER>/workplace/code/kwaishop-flow-live-task/kwaishop-flow-live-task/src
        // /main/java/com/kuaishou/kwaishop/flow/live/task/domain/live/service
        // /MerchantYellowCarStockChangeObserverImpl.java
        FavoriteLeaf leaf = favoriteService.adjustLeaf(leafId);
        path = leaf.getUrl().replace("file://","");
        if (leaf!=null && leaf.getLine() != null) {
            line = leaf.getLine() + "";
        }
//        String cmd = "/Applications/IntelliJ\\ IDEA.app/Contents/MacOS/idea --line " + line + " " + path;
//        System.out.println(cmd);
//        Process process = Runtime.getRuntime().exec(cmd);
//        process.waitFor();


        System.out.println("--line:"+line+",path:"+path);
        // 使用 ProcessBuilder 构建命令
        ProcessBuilder processBuilder = new ProcessBuilder(
                "/Applications/IntelliJ IDEA.app/Contents/MacOS/idea",
                "--line",
                String.valueOf(line),
                path
        );

        // 启动进程
        Process process = processBuilder.start();
        // 等待进程执行完成并获取退出码
        int exitCode = process.waitFor();

        CompletableFuture.runAsync(() -> favoriteService.updateLeafPriority(leafId));


        CompletableFuture.runAsync(() -> favoriteService.adjustLeafWithAround(leafId));
    }

    @PostMapping("/add")
    public Object addFavoriteLeaf(@RequestBody Map<String, Object> params) {
        try {
            System.out.println("addFavoriteLeaf:" + params);
            // 设置默认值
            FavoriteLeaf leaf = new FavoriteLeaf();
            leaf.setParentId(getParentId(leaf, params));
            leaf.setType("java");
            leaf.setCreated(System.currentTimeMillis() / 1000);
            leaf.setModified(System.currentTimeMillis() / 1000);
            leaf.setPriority((int) (System.currentTimeMillis() / 1000));
            String code = MapUtils.getString(params, "code");
            leaf.setCode(code);
            String url = MapUtils.getString(params, "url");
            leaf.setUrl(CodePathHelper.fillPath(url));
            Integer line = MapUtils.getInteger(params, "line") - 1;
            leaf.setLine(line);
            String name = MapUtils.getString(params, "name");
            leaf.setName(name);
            String note = MapUtils.getString(params, "note");
            leaf.setNote(note);

            boolean success = favoriteService.save(leaf);
            if (success) {
                return params;
            } else {
                return "数据保存失败";
            }
        } catch (Exception e) {
            e.printStackTrace();
            return "数据保存出错: " + e.getMessage();
        }
    }

    @PostMapping("/leaf/update")
    public Object updateFavoriteLeaf(@RequestBody JSONObject leafJsonVO) {
        try {
            FavoriteLeafVO leafVO = leafJsonVO.toJavaObject(FavoriteLeafVO.class);
            System.out.println("updateFavoriteLeaf:" + JSON.toJSONString(leafVO));

            // 将 VO 转换为实体
            FavoriteLeaf leaf = FavoriteLeafUtils.convertToEntity(leafVO, favoriteService);

            boolean success = favoriteService.save(leaf);
            if (success) {
                // 将更新后的实体转换回 VO 返回给前端
                return FavoriteLeafUtils.convertToVO(leaf);
            } else {
                return "数据保存失败";
            }
        } catch (Exception e) {
            e.printStackTrace();
            return "数据保存出错: " + e.getMessage();
        }
    }

    @GetMapping("/leaf/delete")
    public Object deleteFavoriteLeaf(@RequestParam(value ="path", required = false) String path, @RequestParam(value ="line", required = false) String line,
            @RequestParam(value = "leafId") Integer leafId) {
        try {

            System.out.println("deleteFavoriteLeaf leafId="+leafId );
            boolean success = favoriteService.deleteLeaf(leafId);
            return "success";
        } catch (Exception e) {
            e.printStackTrace();
            return "数据保存出错: " + e.getMessage();
        }
    }

    private Integer getParentId(FavoriteLeaf leaf, Map<String, Object> paramMap) {

        Integer parentId = MapUtils.getInteger(paramMap, "parentId");
        String parentName = MapUtils.getString(paramMap, "parentName");
        Integer grandfatherId = MapUtils.getInteger(paramMap, "grandfatherId");
        String grandfatherName = MapUtils.getString(paramMap, "grandfatherName");


        if (parentId == null || parentId == -1) {
            if (StringUtils.isNotBlank(parentName)) {
                //新增
                FavoriteBranch branch = new FavoriteBranch();
                branch.setName(parentName);
                // 父parentId
                if (grandfatherId != null && grandfatherId != -1) {
                    branch.setParentId(grandfatherId);
                } else {
                    FavoriteBranch topBranch = createTopBranch(grandfatherName);
                    paramMap.put("grandfatherId", topBranch.getId());
                    branch.setParentId(topBranch.getId());
                }
                favoriteService.saveBranch(branch);
                paramMap.put("parentId", branch.getId());
                return branch.getId();
            }


            if (grandfatherId == -1) {
                FavoriteBranch branch = createTopBranch(grandfatherName);
                //返回给前端
                paramMap.put("grandfatherId", branch.getId());
                return branch.getId();
            } else {
                return grandfatherId;
            }
        }

        return parentId;
    }

    private FavoriteBranch createTopBranch(String grandfatherName) {
        FavoriteBranch branch = new FavoriteBranch();
        branch.setName(grandfatherName);
        // 父parentId
        branch.setParentId(0);
        favoriteService.saveBranch(branch);
        return branch;
    }


    @GetMapping("/branch/search")
    public List branchSearch(@RequestParam("keyword") String keyword,
            @RequestParam(value = "parentBranchId", required = false) String parentBranchId) {
        try {

            List<FavoriteBranch> branches = favoriteService.searchBranch(keyword, parentBranchId);
            if (branches == null || branches.isEmpty()) {
                return branches;
            }

            List<Integer> parentBranchIdList = branches.stream().map(FavoriteBranch::getParentId).distinct().toList();
            List<FavoriteBranch> parentList = favoriteService.getBranchByIds(parentBranchIdList);

            List<FavoriteBranchVO> favoriteBranchVOS = new ArrayList<>();
            for (FavoriteBranch branch : branches) {
                FavoriteBranchVO favoriteBranchVO = new FavoriteBranchVO();
                favoriteBranchVO.setId(branch.getId());
                favoriteBranchVO.setName(branch.getName());
                favoriteBranchVO.setParentId(branch.getParentId());
                parentList.stream().filter(parent -> parent.getId().equals(branch.getParentId()))
                        .findFirst().ifPresent(parent -> favoriteBranchVO.setParentName(parent.getName()));
                favoriteBranchVO.setCreated(branch.getCreated());
                favoriteBranchVO.setModified(branch.getModified());
                favoriteBranchVO.setPriority(branch.getPriority());
                favoriteBranchVOS.add(favoriteBranchVO);
            }
            return favoriteBranchVOS;
        } catch (Exception e) {
            e.printStackTrace();
            return Arrays.asList(e.getMessage());
        }
    }
}