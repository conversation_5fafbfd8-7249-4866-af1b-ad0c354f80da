package com.wade.code.controller;

import static org.apache.commons.collections.CollectionUtils.isNotEmpty;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.wade.code.convert.TagConverter;
import com.wade.code.dao.FavoriteLeaf;
import com.wade.code.dao.Tag;
import com.wade.code.mapper.FavoriteLeafMapper;
import com.wade.code.mapper.TagMapper;
import com.wade.code.model.TagVO;
import com.wade.code.service.TagService;

@RestController
@RequestMapping("/api/tag")
@CrossOrigin(origins = "*")
public class TagController {

    @Autowired
    private TagService tagService;
    @Autowired
    private TagMapper tagMapper;
    @Autowired
    private TagConverter2 tagConverter;

    /**
     * 添加标签
     */
    @PostMapping("/add")
    public Object addTag(@RequestBody Tag tag) {
        try {
            boolean success = tagService.addTag(tag);
            if (success) {
                return tag;
            } else {
                return "添加标签失败";
            }
        } catch (Exception e) {
            e.printStackTrace();
            return "添加标签出错: " + e.getMessage();
        }
    }

    /**
     * 删除标签
     */
    @DeleteMapping("/delete/{id}")
    public Object deleteTag(@PathVariable Integer id) {
        try {
            boolean success = tagService.deleteTag(id);
            if (success) {
                return "删除标签成功";
            } else {
                return "删除标签失败";
            }
        } catch (Exception e) {
            e.printStackTrace();
            return "删除标签出错: " + e.getMessage();
        }
    }


    /**
     * 获取所有标签
     */
    @GetMapping("/all")
    public List<TagVO> getAllTags() {
        List<Tag> allTags = tagService.getAllTags();
        return tagConverter.toVOs(allTags, tagMapper);
    }

    /**
     * 获取所有标签
     */
    @GetMapping("/listCateTags")
    public Map<String, List<TagVO>> listCateTags() {
        List<Tag> allTags = tagService.getAllParentTags();
        List<TagVO> allTagVOs = tagConverter.toVOs(allTags, tagMapper);
        Map<String, List<TagVO>> map = allTagVOs.stream().filter(tag -> !tag.getName().contains("tag"))
                .collect(Collectors.groupingBy(TagVO::getCate1));
        return map;
    }

    /**
     * 获取所有标签
     */
    @GetMapping("/getSubTags")
    public List<TagVO> getSubTags(@RequestParam("parentId") Integer parentId) {
        List<Tag> allTags = tagService.getSubTags(parentId);
        return tagConverter.toVOs(allTags, tagMapper);
    }

    /**
     * 根据标签名称查询标签
     */
    @GetMapping("/byName")
    public List<TagVO> getTagsByName(@RequestParam("name") String name) {
        List<Tag> tagsByName = tagService.getTagsByName(name);
        return tagConverter.toVOs(tagsByName, tagMapper);
    }

    /**
     * 更新标签
     */
    @PutMapping("/update")
    public Object updateTag(@RequestBody Tag tag) {
        try {
            boolean success = tagService.updateTag(tag);
            if (success) {
                return tag;
            } else {
                return "更新标签失败";
            }
        } catch (Exception e) {
            e.printStackTrace();
            return "更新标签出错: " + e.getMessage();
        }
    }


    @Autowired
    private FavoriteLeafMapper favoriteLeafMapper;

    @PostMapping("/repair")
    public Object repair() {

        Integer[] leafIds =
                new Integer[] {675, 676, 806, 809, 811, 983, 995, 1037, 1469, 1490, 1494, 1509, 1510, 1555, 1629, 1874};
        List<FavoriteLeaf> favoriteLeaves = favoriteLeafMapper.selectByIds(Arrays.asList(leafIds));
        for (FavoriteLeaf favoriteLeaf : favoriteLeaves) {
            String originTagBitmaps = favoriteLeaf.getTagBitmaps();
            String[] splits = originTagBitmaps.split("#");
            String finalBitmap = "";
            List<String> segmentList = new ArrayList<>();
            for (String split : splits) {
                String tagNumberStr = split.substring(Math.max(0, split.length() - 10));
                segmentList.add(tagNumberStr);
            }

            finalBitmap = StringUtils.join(segmentList, "#");
            System.out.println("originTagBitmaps" + originTagBitmaps + ",finalBitmap: " + finalBitmap);
            favoriteLeaf.setTagBitmaps(finalBitmap);
            favoriteLeafMapper.updateById(favoriteLeaf);
        }
        return "ok";
    }


    @GetMapping("/addTags")
    public Object addTags(@RequestParam("tagNames") String tagNames
            , @RequestParam(value = "cate1", required = false) String cate1) {

        String[] names = tagNames.split(",");
        for (String name : names) {
            HashMap<String, Object> nameMap = new HashMap<>();
            nameMap.put("name", name);
            List<Tag> tags = tagMapper.selectByMap(nameMap);
            if (isNotEmpty(tags)) {
                System.out.println("tag:" + name + " 已存在");
                if (StringUtils.isNotBlank(cate1)) {
                    Tag tag = tags.get(0);
                    tag.setCate1(cate1);
                    tagService.updateTag(tag);
                    System.out.println("tag - cate1:" + name + " 更新成功");
                }
                continue;
            }
            Tag tag = new Tag();
            QueryWrapper<Tag> queryWrapper = new QueryWrapper<>();
            queryWrapper.orderByDesc("tag_index").last("limit 1");
            Tag maxIndexTag = tagMapper.selectOne(queryWrapper);
            tag.setName(name);
            tag.setDescription(name);
            tag.setCate1(cate1);
            tag.setTagIndex(maxIndexTag.getTagIndex() + 1);
            tag.setCreated(System.currentTimeMillis() / 1000);
            tag.setModified(System.currentTimeMillis() / 1000);
            boolean b = tagService.addTag(tag);
            System.out.println("tag:" + name + " 添加成功");
        }
        return "ok";
    }


    @GetMapping("/autoAddTags")
    public Object autoAddTags(@RequestParam(value = "leafId", required = false) Long leafId
            , @RequestParam(value = "cate1", required = false) String cate1
            , @RequestParam(value = "tagNames", required = false) String tagNames
    ) {
        QueryWrapper<Tag> tagQueryWrapper = new QueryWrapper<>();
        if (StringUtils.isNotBlank(tagNames)) {
            tagQueryWrapper.in("name", Arrays.asList(tagNames.split(",")));
        }
        List<Tag> allTags = tagMapper.selectList(tagQueryWrapper);


        QueryWrapper<FavoriteLeaf> queryWrapper = new QueryWrapper<>();
        if (leafId != null) {
            queryWrapper.eq("id", leafId);
        }

        List<FavoriteLeaf> favoriteLeaves = favoriteLeafMapper.selectList(queryWrapper);
        for (FavoriteLeaf leaf : favoriteLeaves) {
            String name = leaf.getName();
            if (StringUtils.isBlank(name)) {
                continue;
            }
            for (Tag tag : allTags) {
                String lowerName = StringUtils.lowerCase(name);
                String lowerTagName = StringUtils.lowerCase(tag.getName());
                if (StringUtils.contains(lowerName, lowerTagName) || StringUtils.contains(lowerTagName, lowerName)) {
                    tagService.addTag(leaf, tag.getId());
                }
            }
        }

        return "ok";
    }


    @GetMapping("/autoRemoveTags")
    public Object autoRemoveTags(@RequestParam(value = "tagNames") String tagNames) {

        List<Tag> removeTags =
                tagMapper.selectList(new QueryWrapper<Tag>().in("name", Arrays.asList(tagNames.split(","))));

        QueryWrapper<FavoriteLeaf> queryWrapper = new QueryWrapper<>();
        List<FavoriteLeaf> favoriteLeaves = favoriteLeafMapper.selectList(queryWrapper);
        for (FavoriteLeaf leaf : favoriteLeaves) {
            String name = leaf.getName();
            if (StringUtils.isBlank(name)) {
                continue;
            }
            for (Tag tag : removeTags) {
                String lowerName = StringUtils.lowerCase(name);
                String lowerTagName = StringUtils.lowerCase(tag.getName());
                if (StringUtils.contains(lowerName, lowerTagName) || StringUtils.contains(lowerTagName, lowerName)) {
                    tagService.removeTag(leaf, tag);
                }
            }
        }

        return "ok";
    }
}
