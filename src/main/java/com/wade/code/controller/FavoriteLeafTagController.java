package com.wade.code.controller;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.wade.code.convert.FavoriteLeafConverter;
import com.wade.code.dao.FavoriteBranch;
import com.wade.code.dao.FavoriteLeaf;
import com.wade.code.dao.FavoriteLeafTag;
import com.wade.code.dao.Tag;
import com.wade.code.mapper.FavoriteBranchMapper;
import com.wade.code.mapper.TagMapper;
import com.wade.code.model.SearchResult;
import com.wade.code.service.FavoriteLeafTagService;
import com.wade.code.utils.CodeMapCache;
import com.wade.code.utils.FavoriteLeafUtils;

@RestController
@RequestMapping("/api/favorite/tag")
@CrossOrigin(origins = "*")
public class FavoriteLeafTagController {

    @Autowired
    private FavoriteLeafTagService favoriteLeafTagService;

    /**
     * 添加标签
     */
    @PostMapping("/add")
    public Object addTag(@RequestBody FavoriteLeafTag tag) {
        try {
            System.out.println("添加标签: " + tag);
            boolean success = favoriteLeafTagService.addTag(tag);
            if (success) {
                return tag;
            } else {
                return "添加标签失败";
            }
        } catch (Exception e) {
            e.printStackTrace();
            return "添加标签出错: " + e.getMessage();
        }
    }

    /**
     * 查询标签
     */
    @GetMapping("/search")
    public List<Tag> search(@RequestParam("keyword") String keyword) {
        try {
            System.out.println("search: keyword " + keyword);
            // 使用TagMapper进行like查询
            QueryWrapper<Tag> queryWrapper = new QueryWrapper<>();
            queryWrapper.like("name", keyword);
            List<Tag> tags = tagMapper.selectList(queryWrapper);
            return tags;
        } catch (Exception e) {
            e.printStackTrace();
            return new ArrayList<>();
        }
    }


    @Autowired
    CodeMapCache coeMapCache;

    @Autowired
    private FavoriteBranchMapper favoriteBranchMapper;

    @Autowired
    private TagMapper tagMapper;

    @GetMapping("/searchLeafByTags")
    public Object searchLeafByTags(@RequestParam("tagIds") String tags, @RequestParam("keyword") String keyword) {
        try {
            System.out.println("searchLeafByTags request tagIds: " + tags);

            String[] tagIds = tags.split(",");
            Set<Integer> tagIndexes = new HashSet<>();
            Set<Integer> orTagIndexes = new HashSet<>();

            for (String tagId : tagIds) {
                Tag tag = coeMapCache.getTag(Integer.parseInt(tagId));
                if (StringUtils.isNotBlank(tag.getChildren())) {
                    String[] children = tag.getChildren().split(",");
                    for (String childTagId : children) {
                        Tag childTag = coeMapCache.getTag(Integer.parseInt(childTagId));
                        orTagIndexes.add(childTag.getTagIndex());
                        orTagIndexes.add(tag.getTagIndex());
                    }
                } else {
                    tagIndexes.add(tag.getTagIndex());
                }
            }
            List<FavoriteLeaf> favoriteLeaves = favoriteLeafTagService.listLeafByTags(
                    new ArrayList<>(tagIndexes)
                    , new ArrayList<>(orTagIndexes)
                    , keyword);
            FavoriteLeafUtils.convertToVOList(favoriteLeaves);
            // 合并结果
            List<SearchResult> results = new ArrayList<>();

            // 处理从 leaf 搜索出来的结果
            for (FavoriteLeaf leaf : favoriteLeaves) {
                FavoriteBranch branch = favoriteBranchMapper.selectById(leaf.getParentId());
                SearchResult searchResult = new SearchResult();
                searchResult.setBranch(branch);
                searchResult.setLeaf(FavoriteLeafUtils.convertToVO(leaf));
                if (branch != null && branch.getParentId() != null) {
                    searchResult.setGrandfatherBranch(coeMapCache.getBranch(branch.getParentId()));
                }
                results.add(searchResult);
            }
            return results;
        } catch (Exception e) {
            e.printStackTrace();
            return "listLeafByTags: " + e.getMessage();
        }
    }

    /**
     * 删除标签
     */
    @DeleteMapping("/delete/{id}")
    public Object deleteTag(@PathVariable Integer id) {
        try {
            boolean success = favoriteLeafTagService.deleteTag(id);
            if (success) {
                return "删除标签成功";
            } else {
                return "删除标签失败";
            }
        } catch (Exception e) {
            e.printStackTrace();
            return "删除标签出错: " + e.getMessage();
        }
    }

    /**
     * 根据叶子节点ID查询标签
     */
    @GetMapping("/byLeafId")
    public List<FavoriteLeafTag> getTagsByLeafId(@RequestParam("leafId") Integer leafId) {
        return favoriteLeafTagService.getTagsByLeafId(leafId);
    }

    /**
     * 根据标签名称查询标签
     */
    @GetMapping("/byName")
    public List<FavoriteLeafTag> getTagsByName(@RequestParam("tagName") String tagName) {
        return favoriteLeafTagService.getTagsByName(tagName);
    }

    /**
     * 更新标签
     */
    @PutMapping("/update")
    public Object updateTag(@RequestBody FavoriteLeafTag tag) {
        try {
            boolean success = favoriteLeafTagService.updateTag(tag);
            if (success) {
                return tag;
            } else {
                return "更新标签失败";
            }
        } catch (Exception e) {
            e.printStackTrace();
            return "更新标签出错: " + e.getMessage();
        }
    }
}
