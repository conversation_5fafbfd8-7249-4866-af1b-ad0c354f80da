package com.wade.code.model;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

import lombok.Data;
import java.util.ArrayList;
import java.util.List;

@Data
public class TreeNode {
    private Integer id;
    private String name;
    private List<TreeNode> children = new ArrayList<>();

    public TreeNode(Integer id, String name) {
        this.id = id;
        this.name = name;
    }
}