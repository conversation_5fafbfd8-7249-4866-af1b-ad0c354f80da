package com.wade.code.model;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import lombok.Data;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2025-05-23
 */
@Data
public class GraphNodeLinkInfo {

    private GraphNode curNode;
    private List<GraphNode> preNodes = new ArrayList<>();
    private List<GraphEdge> preEdges = new ArrayList<>();

    private List<GraphNode> nextNodes = new ArrayList<>();
    private List<GraphEdge> nextEdges = new ArrayList<>();
}
