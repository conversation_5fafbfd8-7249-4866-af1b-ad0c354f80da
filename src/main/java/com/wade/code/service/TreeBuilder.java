package com.wade.code.service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.wade.code.dao.FavoriteBranch;
import com.wade.code.model.TreeNode;

public class TreeBuilder {
    public static TreeNode buildTree(List<FavoriteBranch> branches) {
        // 用于快速根据 id 查找对应的 TreeNode
        Map<Integer, TreeNode> nodeMap = new HashMap<>();
        // 自定义根节点
        TreeNode root = new TreeNode(0, "Root");

        // 先将所有的 FavoriteBranch 转换为 TreeNode 并存储到 nodeMap 中
        for (FavoriteBranch branch : branches) {
            TreeNode node = new TreeNode(branch.getId(), branch.getName());
            nodeMap.put(branch.getId(), node);
        }

        // 遍历所有节点，构建树结构
        for (FavoriteBranch branch : branches) {
            TreeNode currentNode = nodeMap.get(branch.getId());
            Integer parentId = branch.getParentId();
            if (parentId == null || parentId == 0) {
                // 如果该节点的父节点 ID 为 null 或 0，则将其添加到自定义根节点下
                root.getChildren().add(currentNode);
            } else {
                // 找到父节点并将当前节点添加到父节点的子节点列表中
                TreeNode parentNode = nodeMap.get(parentId);
                if (parentNode != null) {
                    parentNode.getChildren().add(currentNode);
                }
            }
        }

        // 对每个节点的子节点列表按照 name 升序排序
        sortChildrenRecursively(root);
        return root;
    }


    private static void sortChildrenRecursively(TreeNode node) {
        if (node == null) {
            return;
        }
        List<TreeNode> children = node.getChildren();
        if (children != null && !children.isEmpty()) {
            // 对当前节点的子节点列表按照 name 升序排序
            List<TreeNode> sortedChildren = children.stream()
                    .sorted((n1, n2) -> n1.getName().compareTo(n2.getName()))
                    .collect(Collectors.toList());
            node.setChildren(sortedChildren);

            // 递归调用，对每个子节点的子节点列表进行排序
            for (TreeNode child : sortedChildren) {
                sortChildrenRecursively(child);
            }
        }
    }
}