package com.wade.code.service;

import java.util.Collections;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.wade.code.convert.LeafGraphConverter;
import com.wade.code.dao.LeafGraph;
import com.wade.code.mapper.LeafGraphMapper;
import com.wade.code.model.GraphNodeLinkInfo;

/**
 * LeafGraph 服务实现类
 */
@Service
public class LeafGraphServiceImpl implements LeafGraphService {

    @Autowired
    private LeafGraphMapper leafGraphMapper;

    @Override
    public boolean save(LeafGraph leafGraph) {
        // 设置创建和修改时间
        long currentTime = System.currentTimeMillis() / 1000;
        leafGraph.setCreated(currentTime);
        leafGraph.setModified(currentTime);
        return leafGraphMapper.insertOrUpdate(leafGraph);
    }

    @Override
    public GraphNodeLinkInfo getById(Integer id) {
        LeafGraph leafGraph = leafGraphMapper.selectById(id);
        return  LeafGraphConverter.INSTANCE.toLinkInfo(leafGraph);
    }

    @Override
    public List<LeafGraph> getByName(String curName) {
        if (StringUtils.isBlank(curName)) {
            return Collections.emptyList();
        }
        
        QueryWrapper<LeafGraph> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("cur_name", curName);
        return leafGraphMapper.selectList(queryWrapper);
    }

    @Override
    public List<LeafGraph> listAll() {
        return leafGraphMapper.selectList(null);
    }

    @Override
    public boolean update(LeafGraph leafGraph) {
        // 更新修改时间
        leafGraph.setModified(System.currentTimeMillis() / 1000);
        return leafGraphMapper.updateById(leafGraph) > 0;
    }

    @Override
    public boolean delete(Integer id) {
        return leafGraphMapper.deleteById(id) > 0;
    }
}
