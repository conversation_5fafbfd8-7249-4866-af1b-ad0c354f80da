package com.wade.code.service;

import java.util.List;


import com.wade.code.dao.FavoriteBranch;
import com.wade.code.dao.FavoriteLeaf;
import com.wade.code.model.SearchResult;
import com.wade.code.model.TreeNode;

public interface FavoriteService {
    TreeNode getAllLeafs();

    List<FavoriteLeaf> getLeafsByBranchId(Integer branchId);

    void renameBranch(Integer id, String name);

    void moveNode(Integer dragId, Integer dropId);

    List<SearchResult> searchCode(String keyword);

    boolean save(FavoriteLeaf leaf);

    List<FavoriteBranch> searchBranch(String keyword, String parentBranchId);

    void saveBranch(FavoriteBranch branch);

    void updateLeafPriority(Integer leafId);

    FavoriteLeaf adjustLeaf(Integer leafId);

    void adjustLeafWithAround(Integer leafId);

    List<FavoriteBranch> getBranchByIds(List<Integer> parentBranchIdList);

    void moveLeafPath(Favorite<PERSON>ea<PERSON> leaf, Integer branchId);

    boolean deleteLeaf(Integer leafId);

    void deleteBranch(Integer branchId);

    FavoriteLeaf getLeaf(Integer leafId);

}
