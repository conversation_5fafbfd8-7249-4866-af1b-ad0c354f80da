package com.wade.code.service;

import java.util.List;

import com.wade.code.dao.FavoriteLeaf;
import com.wade.code.dao.Tag;

public interface TagService {
    /**
     * 添加标签
     * @param tag 标签信息
     * @return 是否添加成功
     */
    boolean addTag(Tag tag);


    boolean addTag(FavoriteLeaf leaf ,Integer tagId) ;

    boolean removeTag(FavoriteLeaf leaf ,Tag tag) ;

        /**
         * 删除标签
         * @param id 标签ID
         * @return 是否删除成功
         */
    boolean deleteTag(Integer id);
    
    /**
     * 获取所有标签
     * @return 标签列表
     */
    List<Tag> getAllTags();

    List<Tag> getAllParentTags();


    List<Tag> getSubTags(Integer parentId) ;
    
    /**
     * 根据标签名称查询标签
     * @param name 标签名称
     * @return 标签列表
     */
    List<Tag> getTagsByName(String name);
    
    /**
     * 更新标签信息
     * @param tag 标签信息
     * @return 是否更新成功
     */
    boolean updateTag(Tag tag);
    
    /**
     * 根据ID获取标签
     * @param id 标签ID
     * @return 标签信息
     */
    Tag getTagById(Integer id);
}
