package com.wade.code.service;

import java.util.List;

import com.wade.code.dao.FavoriteLeaf;
import com.wade.code.dao.FavoriteLeafTag;

public interface FavoriteLeafTagService {
    List<FavoriteLeaf> listLeafByTags(List<Integer> tagIndex,String keyword);

    /**
     * 添加叶子节点标签
     * @param tag 标签信息
     * @return 是否添加成功
     */
    boolean addTag(FavoriteLeafTag tag);
    
    /**
     * 删除叶子节点标签
     * @param id 标签ID
     * @return 是否删除成功
     */
    boolean deleteTag(Integer id);
    
    /**
     * 根据叶子节点ID查询标签
     * @param leafId 叶子节点ID
     * @return 标签列表
     */
    List<FavoriteLeafTag> getTagsByLeafId(Integer leafId);
    
    /**
     * 根据标签名称查询标签
     * @param tagName 标签名称
     * @return 标签列表
     */
    List<FavoriteLeafTag> getTagsByName(String tagName);
    
    /**
     * 更新标签信息
     * @param tag 标签信息
     * @return 是否更新成功
     */
    boolean updateTag(FavoriteLeafTag tag);
}
