package com.wade.code.service;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.wade.code.dao.FavoriteLeaf;
import com.wade.code.dao.FavoriteLeafTag;
import com.wade.code.dao.Tag;
import com.wade.code.mapper.FavoriteLeafMapper;
import com.wade.code.mapper.TagMapper;
import com.wade.code.utils.CodeMapCache;
import com.wade.code.utils.TagUtils;

@Service
public class TagServiceImpl implements TagService {

    @Autowired
    private TagMapper tagMapper;

    @Override
    public boolean addTag(Tag tag) {
        // 设置创建和修改时间
        tag.setCreated(System.currentTimeMillis() / 1000);
        tag.setModified(System.currentTimeMillis() / 1000);
        return tagMapper.insert(tag) > 0;
    }


    @Autowired
    private CodeMapCache codeMapCache;
    @Autowired
    private FavoriteLeafMapper leafMapper;

    @Override
    public boolean addTag(FavoriteLeaf leaf, Integer tagId) {
        Tag tag = codeMapCache.getTag(tagId);
        List<Integer> tagIndexes = TagUtils.getTagIndexes(leaf.getTagBitmaps());
        Integer tagIndex = tag.getTagIndex();
        if (tagIndexes.contains(tagIndex)) {
            return true;
        }

        tagIndexes.add(tagIndex);
        String finalBitMap = TagUtils.databaseTagNumberStr(tagIndexes);
        leaf.setTagBitmaps(finalBitMap);
        leaf.setModified(System.currentTimeMillis() / 1000);
        if (leafMapper.updateById(leaf) > 0) {
            System.out.println("addTag success ,leaf: " + leaf.getId() + ",name:" + leaf.getName() + ",addTag: " + tag.getName() + ",tagId="
                            + tag.getId());
            return true;
        }
        return false;
    }

    @Override
    public boolean removeTag(FavoriteLeaf leaf, Tag removeTag) {
        List<Integer> tagIndexes = TagUtils.getTagIndexes(leaf.getTagBitmaps());
        Integer rmTagIndex = removeTag.getTagIndex();
        if (!tagIndexes.contains(rmTagIndex)) {
            return true;
        }

        tagIndexes.remove(rmTagIndex);
        String finalBitMap = TagUtils.databaseTagNumberStr(tagIndexes);
        leaf.setTagBitmaps(finalBitMap);
        leaf.setModified(System.currentTimeMillis() / 1000);
        if (leafMapper.updateById(leaf) > 0) {
            System.out.println(
                    "removeTag success ,leaf: " + leaf.getId() + ",name:" + leaf.getName() + ",removeTag: " + removeTag.getName() + ",tagId="
                            + removeTag.getId());
            return true;
        }
        return false;
    }

    @Override
    public boolean deleteTag(Integer id) {
        return tagMapper.deleteById(id) > 0;
    }

    @Override
    public List<Tag> getAllTags() {
        return tagMapper.selectList(null);
    }


    @Override
    public List<Tag> getAllParentTags() {
        QueryWrapper<Tag> queryWrapper = new QueryWrapper<>();
        queryWrapper.isNull("parent_id");
        return tagMapper.selectList(queryWrapper);
    }

    @Override
    public List<Tag> getSubTags(Integer parentId) {
        QueryWrapper<Tag> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("parent_id", parentId);
        return tagMapper.selectList(queryWrapper);
    }

    @Override
    public List<Tag> getTagsByName(String name) {
        QueryWrapper<Tag> queryWrapper = new QueryWrapper<>();
        queryWrapper.like("name", name);
        return tagMapper.selectList(queryWrapper);
    }

    @Override
    public boolean updateTag(Tag tag) {
        // 更新修改时间
        tag.setModified(System.currentTimeMillis() / 1000);
        return tagMapper.updateById(tag) > 0;
    }

    @Override
    public Tag getTagById(Integer id) {
        return tagMapper.selectById(id);
    }
}
