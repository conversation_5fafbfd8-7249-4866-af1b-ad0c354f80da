package com.wade.code.service;

import java.util.List;

import com.wade.code.dao.LeafGraph;
import com.wade.code.model.GraphNodeLinkInfo;

/**
 * LeafGraph 服务接口
 */
public interface LeafGraphService {
    /**
     * 保存叶子图谱
     * @param leafGraph 叶子图谱信息
     * @return 是否保存成功
     */
    boolean save(LeafGraph leafGraph);
    
    /**
     * 根据ID获取叶子图谱
     *
     * @param id 叶子图谱ID
     * @return 叶子图谱信息
     */
    GraphNodeLinkInfo getById(Integer id);
    
    /**
     * 根据名称查询叶子图谱
     * @param curName 叶子名称
     * @return 叶子图谱列表
     */
    List<LeafGraph> getByName(String curName);
    
    /**
     * 获取所有叶子图谱
     * @return 叶子图谱列表
     */
    List<LeafGraph> listAll();
    
    /**
     * 更新叶子图谱信息
     * @param leafGraph 叶子图谱信息
     * @return 是否更新成功
     */
    boolean update(LeafGraph leafGraph);
    
    /**
     * 删除叶子图谱
     * @param id 叶子图谱ID
     * @return 是否删除成功
     */
    boolean delete(Integer id);
}
