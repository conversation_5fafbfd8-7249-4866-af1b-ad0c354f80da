package com.wade.code.service;

import static com.wade.code.constant.Constant.TAG_SEGMENT_STRING_LENGTH;
import static org.apache.commons.collections.CollectionUtils.isEmpty;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.wade.code.dao.FavoriteLeaf;
import com.wade.code.dao.FavoriteLeafTag;
import com.wade.code.dao.Tag;
import com.wade.code.mapper.FavoriteLeafMapper;
import com.wade.code.mapper.FavoriteLeafTagMapper;
import com.wade.code.mapper.TagMapper;
import com.wade.code.utils.TagUtils;

@Service
public class FavoriteLeafTagServiceImpl implements FavoriteLeafTagService {

    @Autowired
    private FavoriteLeafTagMapper favoriteLeafTagMapper;

    @Autowired
    private FavoriteLeafMapper leafMapper;

    @Autowired
    private TagMapper tagMapper;


    @Override
    public boolean addTag(FavoriteLeafTag tag) {
        return true;
    }

    public boolean updateTags(Integer leafId, List<Integer> addTagIndexes, List<Integer> removeTagIndexes) {
        if (leafId == null) {
            return false;
        }

        try {
            // 数据库中获取 leaf.tagBitmaps 的二进制字符串
            FavoriteLeaf leaf = leafMapper.selectById(leafId);
            if (leaf == null) {
                return false;
            }

            String tagBitmaps = leaf.getTagBitmaps();

            // 确定需要的字符串长度
            int maxIndex = 0;

            // 找出添加标签中的最大索引
            if (addTagIndexes != null && !addTagIndexes.isEmpty()) {
                for (Integer index : addTagIndexes) {
                    if (index != null && index > maxIndex) {
                        maxIndex = index;
                    }
                }
            }

            // 找出移除标签中的最大索引
            if (removeTagIndexes != null && !removeTagIndexes.isEmpty()) {
                for (Integer index : removeTagIndexes) {
                    if (index != null && index > maxIndex) {
                        maxIndex = index;
                    }
                }
            }

            // 创建字符数组，确保足够长
            StringBuilder bitmapsBuilder = new StringBuilder(tagBitmaps != null ? tagBitmaps : "");
            while (bitmapsBuilder.length() <= maxIndex) {
                bitmapsBuilder.append('0');
            }

            // 将 tagBitmaps 的 addTagIndexes 位数更新为 1
            if (addTagIndexes != null) {
                for (Integer index : addTagIndexes) {
                    if (index != null && index >= 0 && index < bitmapsBuilder.length()) {
                        bitmapsBuilder.setCharAt((maxIndex - index), '1');
                    }
                }
            }

            // 将 tagBitmaps 的 removeTagIndexes 位数设置为 0
            if (removeTagIndexes != null) {
                for (Integer index : removeTagIndexes) {
                    if (index != null && index >= 0 && index < bitmapsBuilder.length()) {
                        bitmapsBuilder.setCharAt((maxIndex - index), '0');
                    }
                }
            }

            // 更新叶子节点
            FavoriteLeaf updateLeaf = new FavoriteLeaf();
            updateLeaf.setId(leafId);
            updateLeaf.setTagBitmaps(bitmapsBuilder.toString());
            updateLeaf.setModified(System.currentTimeMillis() / 1000);

            System.out.println("更新后的 tagBitmaps: " + bitmapsBuilder.toString());
            // 使用 leafMapper 更新数据库
            return leafMapper.updateById(updateLeaf) > 0;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    @Override
    public boolean deleteTag(Integer id) {
        return favoriteLeafTagMapper.deleteById(id) > 0;
    }

    @Override
    public List<FavoriteLeafTag> getTagsByLeafId(Integer leafId) {
        QueryWrapper<FavoriteLeafTag> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("leaf_id", leafId);
        return favoriteLeafTagMapper.selectList(queryWrapper);
    }

    @Override
    public List<FavoriteLeafTag> getTagsByName(String tagName) {
        QueryWrapper<FavoriteLeafTag> queryWrapper = new QueryWrapper<>();
        queryWrapper.like("tag_name", tagName);
        return favoriteLeafTagMapper.selectList(queryWrapper);
    }

    @Override
    public List<FavoriteLeaf> listLeafByTags(List<Integer> tagIndexes ,List<Integer> orTagIndexes, String keyword) {
        // 如果标签索引列表为空，返回空列表
        if (CollectionUtils.isEmpty(tagIndexes) &&  CollectionUtils.isEmpty(orTagIndexes)) {
            return Collections.emptyList();
        }

        try {
            // 将 tagIndex 根据 64位进行分段
            Map<Integer, List<Integer>> segmentedTagIndexes = TagUtils.getSegmentedTagIndexes(tagIndexes);
            Map<Integer, List<Integer>> orSegmentedTagIndexes = TagUtils.getSegmentedTagIndexes(orTagIndexes);

            // 如果没有有效的标签索引，返回空列表
            if (segmentedTagIndexes.isEmpty() && orSegmentedTagIndexes.isEmpty()) {
                return Collections.emptyList();
            }

            // 构建查询条件
            QueryWrapper<FavoriteLeaf> queryWrapper = new QueryWrapper<>();

            if(!orSegmentedTagIndexes.isEmpty()){
                applyOrTagSql(orSegmentedTagIndexes, queryWrapper);
            }else{
                applyTagSql(segmentedTagIndexes, queryWrapper);
            }

            if (StringUtils.isNotBlank(keyword)) {
                queryWrapper.nested(w -> w
                        .like("name", keyword)
                        .or().like("code", keyword)
                );
            }

            //排序
            queryWrapper.orderByDesc("priority");

            // 执行查询
            return leafMapper.selectList(queryWrapper);
        } catch (Exception e) {
            e.printStackTrace();
            return Collections.emptyList();
        }
    }

    private static void applyTagSql(Map<Integer, List<Integer>> segmentedTagIndexes,
            QueryWrapper<FavoriteLeaf> queryWrapper) {
        // 对每一段构建查询条件
        for (Map.Entry<Integer, List<Integer>> entry : segmentedTagIndexes.entrySet()) {
            int segmentIndex = entry.getKey();
            List<Integer> indexesInSegment = entry.getValue();

            Long tagNumber = 0L;
            for (Integer i : indexesInSegment) {
                tagNumber = (1L << i) + tagNumber;
            }

            String listLeafByTags =
                    "(SUBSTR(tag_bitmaps, " + (1 + segmentIndex * (TAG_SEGMENT_STRING_LENGTH + 1)) + ", "
                            + TAG_SEGMENT_STRING_LENGTH + ") & '" + tagNumber + "') = "+tagNumber;

            System.out.println("listLeafByTags sql: " + listLeafByTags);
            queryWrapper.apply(listLeafByTags);

        }
    }

    private static void applyOrTagSql(Map<Integer, List<Integer>> segmentedTagIndexes,
            QueryWrapper<FavoriteLeaf> queryWrapper) {
        String sql = "";
        List<String> subSqls = new ArrayList<>();
        // 对每一段构建查询条件
        for (Map.Entry<Integer, List<Integer>> entry : segmentedTagIndexes.entrySet()) {
            int segmentIndex = entry.getKey();
            List<Integer> indexesInSegment = entry.getValue();
            Long tagNumber = 0L;
            for (Integer i : indexesInSegment) {
                tagNumber = (1L << i) + tagNumber;
            }
            String listLeafByTags =
                    "(SUBSTR(tag_bitmaps, " + (1 + segmentIndex * (TAG_SEGMENT_STRING_LENGTH + 1)) + ", "
                            + TAG_SEGMENT_STRING_LENGTH + ") & '" + tagNumber + "') > 0";
            subSqls.add(listLeafByTags);
        }

        sql = "( " + StringUtils.join(subSqls," or ") + " )";
        queryWrapper.apply(sql);
    }


    @Override
    public boolean updateTag(FavoriteLeafTag tag) {
        // 更新修改时间
        tag.setModified(System.currentTimeMillis() / 1000);
        return favoriteLeafTagMapper.updateById(tag) > 0;
    }


}
