package com.wade.code.dao;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.wade.code.dao.handler.ByteArrayTypeHandler;


import lombok.Data;

@Data
@TableName("favorite_leaf")
public class FavoriteLeaf {
    @TableId(type = IdType.AUTO)
    private Integer id;
    private String name;
    private String code;
    private Integer parentId;
    private String type;
    private Long created;
    private Long modified;
    private String url;
    private Integer line;
    private Integer deleted;
    private Integer priority;
    private String tagBitmaps;  // 存储二进制字符串，如 "1000101010"
    private String note;  // 备注信息

}