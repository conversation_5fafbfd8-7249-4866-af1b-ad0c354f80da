package com.wade.code.dao;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

@Data
@TableName("tag")
public class Tag {
    @TableId(type = IdType.AUTO)
    private Integer id;
    private Integer parentId;
    private String children;
    private Integer tagIndex;
    private String name;
    private String cate1 = "其他";
    private String cate2;
    private String keywords;
    private String description;
    private Long created;
    private Long modified;
}
