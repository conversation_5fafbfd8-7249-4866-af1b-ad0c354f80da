package com.wade.code.dao;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("middleware_info")
public class MiddlewareInfo {
    @TableId(type = IdType.AUTO)
    private Integer id;
    private String type;
    private String item;
    private String link;
    private String subItem;
    private String codePathList;
}