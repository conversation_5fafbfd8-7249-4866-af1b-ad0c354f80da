package com.wade.code.dao;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

@Data
@TableName("leaf_graph")
public class LeafGraph {
    @TableId(type = IdType.AUTO)
    private Integer id ;
    private String curInfo;
    private String preInfo;
    private String nextInfo;
    private Long created;
    private Long modified;
}
