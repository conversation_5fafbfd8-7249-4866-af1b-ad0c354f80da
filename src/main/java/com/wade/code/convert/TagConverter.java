package com.wade.code.convert;

import java.util.List;
import java.util.stream.Collectors;

import org.mapstruct.AfterMapping;
import org.mapstruct.Context;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

import com.wade.code.dao.Tag;
import com.wade.code.mapper.TagMapper;
import com.wade.code.model.TagVO;

/**
 * Tag 与 TagVO 之间的转换器
 */
@Mapper(componentModel = "spring")
public interface TagConverter2 {
    @Mapping(target = "parentName", ignore = true)
        // 先忽略，再通过自定义方法设置
    TagVO toVO(Tag tag);

    @AfterMapping  // 映射完成后执行
    default void setParentName(Tag tag, @MappingTarget TagVO tagVO, @Context TagMapper tagMapper) {
        if (tag.getParentId() != null) {
            Tag parentTag = tagMapper.selectById(tag.getParentId());
            tagVO.setParentName(parentTag.getName());
        }
    }

    // 自定义集合映射方法，显式调用单个映射
    default List<TagVO> toVOs(List<Tag> tags, @Context TagMapper tagMapper) {
        return tags.stream()
                .map(tag -> toVO(tag, tagMapper)) // 显式传递上下文
                .collect(Collectors.toList());
    }
}