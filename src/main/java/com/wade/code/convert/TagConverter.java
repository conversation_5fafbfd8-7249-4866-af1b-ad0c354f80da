package com.wade.code.convert;

import java.util.List;

import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.springframework.beans.factory.annotation.Autowired;

import com.wade.code.dao.Tag;
import com.wade.code.mapper.TagMapper;
import com.wade.code.model.TagVO;

/**
 * Tag 与 TagVO 之间的转换器
 */
@Mapper(componentModel = "spring")
public abstract class TagConverter {

    @Autowired
    private TagMapper tagMapper;

    @Mapping(target = "parentName", ignore = true)
    public abstract TagVO toVO(Tag tag);

    public abstract List<TagVO> toVOs(List<Tag> tags);

    @AfterMapping  // 映射完成后执行
    void setParentName(Tag tag, @MappingTarget TagVO tagVO) {
        if (tag.getParentId() != null) {
            Tag parentTag = tagMapper.selectById(tag.getParentId());
            if (parentTag != null) {
                tagVO.setParentName(parentTag.getName());
            }
        }
    }

}