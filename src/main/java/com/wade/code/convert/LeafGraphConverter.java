package com.wade.code.convert;

import static org.apache.commons.collections.CollectionUtils.isEmpty;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.wade.code.dao.LeafGraph;
import com.wade.code.model.GraphEdge;
import com.wade.code.model.GraphNode;
import com.wade.code.model.GraphNodeLinkInfo;
import com.wade.code.model.LeafGraphVO;

/**
 * LeafGraph 与 LeafGraphVO 之间的转换器
 */
@Mapper
public interface LeafGraphConverter {

    LeafGraphConverter INSTANCE = Mappers.getMapper(LeafGraphConverter.class);

    /**
     * 将 LeafGraph 转换为 LeafGraphVO
     *
     * @param leafGraph LeafGraph 实体
     * @return LeafGraphVO 视图对象
     */
    LeafGraphVO toVO(LeafGraph leafGraph);

    /**
     * 将 LeafGraph 列表转换为 LeafGraphVO 列表
     *
     * @param leafGraphList LeafGraph 实体列表
     * @return LeafGraphVO 视图对象列表
     */
    List<LeafGraphVO> toVOList(List<LeafGraph> leafGraphList);

    /**
     * 将 LeafGraphVO 转换为 LeafGraph
     *
     * @param leafGraphVO LeafGraphVO 视图对象
     * @return LeafGraph 实体
     */
    LeafGraph toEntity(LeafGraphVO leafGraphVO);

    default GraphNodeLinkInfo toLinkInfo(LeafGraph leafGraph) {
        if (leafGraph == null) {
            return null;
        }
        String preInfo = leafGraph.getPreInfo();
        String nextInfo = leafGraph.getNextInfo();

        JSONObject linkJsonObject = new JSONObject();
        if (StringUtils.isNotBlank(preInfo)) {
            JSONObject preObject = JSON.parseObject(preInfo);
            linkJsonObject.putAll(preObject);
        }
        if (StringUtils.isNotBlank(nextInfo)) {
            JSONObject nextObject = JSON.parseObject(nextInfo);
            linkJsonObject.putAll(nextObject);
        }
        GraphNode curNode = JSON.parseObject(leafGraph.getCurInfo(), GraphNode.class);
        linkJsonObject.put("curNode", curNode);

        return JSON.toJavaObject(linkJsonObject, GraphNodeLinkInfo.class);
    }

    default LeafGraph toEntity(GraphNode curNode, Set<GraphNode> nodes, Set<GraphEdge> edges) {

        if (isEmpty(edges)) {
            return null;
        }

        String curNodeIdStr = curNode.getId();
        if (!StringUtils.isNumeric(curNodeIdStr)) {
            return null;
        }

        LeafGraph leafGraph = new LeafGraph();
        leafGraph.setId(Integer.parseInt(curNodeIdStr));
        List<GraphNode> preNodes = new ArrayList<>();
        List<GraphEdge> preEdges = new ArrayList<>();
        List<GraphNode> nextNodes = new ArrayList<>();
        List<GraphEdge> nextEdges = new ArrayList<>();

        for (GraphEdge edge : edges) {
            String source = edge.getSource();
            String target = edge.getTarget();

            if (source.equals(curNodeIdStr) && StringUtils.isNotBlank(target)) {
                nextEdges.add(edge);
                nodes.stream().filter(node -> target.equals(node.getId())).findFirst().ifPresent(nextNodes::add);
            }
            if (target.equals(curNodeIdStr) && StringUtils.isNotBlank(source)) {
                preEdges.add(edge);
                nodes.stream().filter(node -> source.equals(node.getId())).findFirst().ifPresent(preNodes::add);
            }
        }

        Map<String, Object> preInfo = new HashMap<>();
        preInfo.put("preNodes", preNodes);
        preInfo.put("preEdges", preEdges);
        Map<String, Object> nextInfo = new HashMap<>();
        nextInfo.put("nextNodes", nextNodes);
        nextInfo.put("nextEdges", nextEdges);
        leafGraph.setCurInfo(JSON.toJSONString(curNode));
        leafGraph.setPreInfo(JSON.toJSONString(preInfo));
        leafGraph.setNextInfo(JSON.toJSONString(nextInfo));

        return leafGraph;
    }

}
