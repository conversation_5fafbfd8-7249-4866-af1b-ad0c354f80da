package com.wade.code.convert;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import com.wade.code.dao.FavoriteLeaf;
import com.wade.code.model.FavoriteLeafVO;

/**
 * FavoriteLeaf 与 FavoriteLeafVO 之间的转换器
 */
@Mapper
public interface FavoriteLeafConverter {

    FavoriteLeafConverter INSTANCE = Mappers.getMapper(FavoriteLeafConverter.class);

    /**
     * 将 FavoriteLeaf 转换为 FavoriteLeafVO
     *
     * @param leaf FavoriteLeaf 实体
     * @return FavoriteLeafVO 视图对象
     */
    @Mapping(target = "tags", ignore = true) // 标签需要单独设置
    FavoriteLeafVO toVO(FavoriteLeaf leaf);

    /**
     * 将 FavoriteLeaf 列表转换为 FavoriteLeafVO 列表
     *
     * @param leafList FavoriteLeaf 实体列表
     * @return FavoriteLeafVO 视图对象列表
     */
    List<FavoriteLeafVO> toVOList(List<FavoriteLeaf> leafList);

    /**
     * 将 FavoriteLeafVO 转换为 FavoriteLeaf
     *
     * @param leafVO FavoriteLeafVO 视图对象
     * @return FavoriteLeaf 实体
     */
    @Mapping(target = "tagBitmaps", ignore = true) // 标签位图需要单独处理
    FavoriteLeaf toEntity(FavoriteLeafVO leafVO);

    /**
     * 将 FavoriteLeafVO 列表转换为 FavoriteLeaf 列表
     *
     * @param leafVOList FavoriteLeafVO 视图对象列表
     * @return FavoriteLeaf 实体列表
     */
    List<FavoriteLeaf> toEntityList(List<FavoriteLeafVO> leafVOList);
}