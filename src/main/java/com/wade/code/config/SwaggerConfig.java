package com.wade.code.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class SwaggerConfig {

    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
               .info(new Info()
                       .title("Spring Boot Swagger API 文档")
                       .description("这是一个使用 Spring Boot 和 Swagger 的示例 API 文档")
                       .version("1.0.0"));
    }
}