-- 创建5个标签的SQL语句
INSERT INTO tag (name, description, created, modified) VALUES ('Redis', 'Redis缓存相关代码', strftime('%s','now'), strftime('%s','now'));
INSERT INTO tag (name, description, created, modified) VALUES ('Task', '任务调度相关代码', strftime('%s','now'), strftime('%s','now'));
INSERT INTO tag (name, description, created, modified) VALUES ('MySQL', '数据库相关代码', strftime('%s','now'), strftime('%s','now'));
INSERT INTO tag (name, description, created, modified) VALUES ('Kafka', '消息队列相关代码', strftime('%s','now'), strftime('%s','now'));
INSERT INTO tag (name, description, created, modified) VALUES ('Java', 'Java核心代码', strftime('%s','now'), strftime('%s','now'));
